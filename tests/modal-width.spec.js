const { test, expect } = require('@playwright/test');

test.describe('笔记模态框宽度测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问应用首页
    await page.goto('/');
    
    // 如果需要登录，先进行登录
    const loginForm = page.locator('form[action="/login"]');
    if (await loginForm.isVisible()) {
      await page.fill('input[name="username"]', 'admin');
      await page.fill('input[name="password"]', 'password');
      await page.click('button[type="submit"]');
      await page.waitForURL('/');
    }
  });

  test('发布笔记模态框 - 桌面端宽度测试', async ({ page }) => {
    // 设置桌面端视口大小
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // 点击发布按钮打开模态框
    const postButton = page.locator('#postButton');
    await expect(postButton).toBeVisible();
    await postButton.click();
    
    // 等待模态框出现
    const modal = page.locator('#postModal');
    await expect(modal).toBeVisible();
    
    // 获取模态框内容区域
    const modalContent = modal.locator('.bg-white').first();
    await expect(modalContent).toBeVisible();
    
    // 检查模态框宽度
    const boundingBox = await modalContent.boundingBox();
    expect(boundingBox).not.toBeNull();
    
    // 验证最小宽度 >= 600px
    expect(boundingBox.width).toBeGreaterThanOrEqual(600);
    
    // 验证最大宽度不超过视口的80%
    const maxExpectedWidth = 1920 * 0.8; // 80% of viewport width
    expect(boundingBox.width).toBeLessThanOrEqual(maxExpectedWidth);
    
    // 验证宽度不超过1200px
    expect(boundingBox.width).toBeLessThanOrEqual(1200);
    
    console.log(`发布模态框宽度: ${boundingBox.width}px`);
  });

  test('编辑笔记模态框 - 桌面端宽度测试', async ({ page }) => {
    // 设置桌面端视口大小
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // 查找第一个编辑按钮
    const editButton = page.locator('.edit-post').first();
    if (await editButton.count() > 0) {
      await editButton.click();
      
      // 等待编辑模态框出现
      const editModal = page.locator('#editModal');
      await expect(editModal).toBeVisible();
      
      // 获取模态框内容区域
      const modalContent = editModal.locator('.bg-white').first();
      await expect(modalContent).toBeVisible();
      
      // 检查模态框宽度
      const boundingBox = await modalContent.boundingBox();
      expect(boundingBox).not.toBeNull();
      
      // 验证最小宽度 >= 600px
      expect(boundingBox.width).toBeGreaterThanOrEqual(600);
      
      // 验证最大宽度不超过视口的80%
      const maxExpectedWidth = 1920 * 0.8;
      expect(boundingBox.width).toBeLessThanOrEqual(maxExpectedWidth);
      
      // 验证宽度不超过1200px
      expect(boundingBox.width).toBeLessThanOrEqual(1200);
      
      console.log(`编辑模态框宽度: ${boundingBox.width}px`);
    } else {
      console.log('没有找到可编辑的笔记，跳过编辑模态框测试');
    }
  });

  test('模态框响应式测试 - 不同屏幕尺寸', async ({ page }) => {
    const viewports = [
      { name: '大桌面', width: 1920, height: 1080, expectedMinWidth: 600 },
      { name: '中等桌面', width: 1366, height: 768, expectedMinWidth: 600 },
      { name: '小桌面', width: 1024, height: 768, expectedMinWidth: 600 },
      { name: '平板', width: 768, height: 1024, expectedMaxWidth: 95 }, // 95% of viewport
      { name: '手机', width: 375, height: 667, expectedMaxWidth: 98 }   // 98% of viewport
    ];

    for (const viewport of viewports) {
      console.log(`测试 ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      // 点击发布按钮
      const postButton = page.locator('#postButton');
      await expect(postButton).toBeVisible();
      await postButton.click();
      
      // 等待模态框出现
      const modal = page.locator('#postModal');
      await expect(modal).toBeVisible();
      
      const modalContent = modal.locator('.bg-white').first();
      const boundingBox = await modalContent.boundingBox();
      expect(boundingBox).not.toBeNull();
      
      if (viewport.width > 768) {
        // 桌面端：检查最小宽度
        expect(boundingBox.width).toBeGreaterThanOrEqual(viewport.expectedMinWidth);
        console.log(`${viewport.name} 模态框宽度: ${boundingBox.width}px (≥${viewport.expectedMinWidth}px)`);
      } else {
        // 移动端：检查最大宽度百分比
        const expectedMaxWidth = viewport.width * (viewport.expectedMaxWidth / 100);
        expect(boundingBox.width).toBeLessThanOrEqual(expectedMaxWidth);
        console.log(`${viewport.name} 模态框宽度: ${boundingBox.width}px (≤${expectedMaxWidth}px)`);
      }
      
      // 关闭模态框
      const closeButton = modal.locator('[data-dismiss="modal"]').first();
      await closeButton.click();
      await expect(modal).not.toBeVisible();
    }
  });

  test('模态框打开关闭功能测试', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // 测试发布模态框
    const postButton = page.locator('#postButton');
    await expect(postButton).toBeVisible();
    
    // 打开模态框
    await postButton.click();
    const postModal = page.locator('#postModal');
    await expect(postModal).toBeVisible();
    await expect(postModal).toHaveClass(/show/);
    
    // 关闭模态框
    const closeButton = postModal.locator('[data-dismiss="modal"]').first();
    await closeButton.click();
    await expect(postModal).not.toHaveClass(/show/);
    
    // 测试编辑模态框（如果有笔记的话）
    const editButton = page.locator('.edit-post').first();
    if (await editButton.count() > 0) {
      await editButton.click();
      const editModal = page.locator('#editModal');
      await expect(editModal).toBeVisible();
      await expect(editModal).toHaveClass(/show/);
      
      // 关闭编辑模态框
      const editCloseButton = editModal.locator('[data-dismiss="modal"]').first();
      await editCloseButton.click();
      await expect(editModal).not.toHaveClass(/show/);
    }
  });

  test('模态框内容区域可用性测试', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // 打开发布模态框
    await page.click('#postButton');
    const modal = page.locator('#postModal');
    await expect(modal).toBeVisible();
    
    // 检查编辑器区域是否可见且可用
    const editor = modal.locator('#markdownEditor');
    await expect(editor).toBeVisible();
    await expect(editor).toBeEditable();
    
    // 检查工具栏是否可见
    const toolbar = modal.locator('.markdown-toolbar');
    await expect(toolbar).toBeVisible();
    
    // 检查标签输入区域是否可见
    const tagInput = modal.locator('#tagInput');
    await expect(tagInput).toBeVisible();
    
    // 检查发布按钮是否可见
    const submitButton = modal.locator('button[type="submit"]');
    await expect(submitButton).toBeVisible();
    
    console.log('模态框内容区域功能正常');
  });
});
