<!-- 发布笔记模态框 -->
<div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/70 backdrop-blur-sm animate-fade-in" id="postModal">
    <div class="bg-white my-[2%] mx-auto rounded-2xl w-[90%] min-w-[600px] max-w-[80vw] lg:max-w-[1200px] shadow-2xl border border-gray-300 animate-slide-in overflow-hidden max-h-[85vh] flex flex-col">
        <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 border-b border-gray-200 flex-shrink-0">
            <h5 class="text-lg font-semibold text-gray-800 m-0">发布笔记</h5>
            <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500 close-button" data-dismiss="modal">&times;</button>
        </div>
        <div class="p-8 flex-1 overflow-y-auto">
            <form id="postForm" class="space-y-6">
                <div class="flex flex-col gap-2">
                    <!-- Markdown编辑器工具栏 -->
                    <div class="markdown-toolbar">
                        <button type="button" class="toolbar-btn" data-action="bold" title="粗体">
                            <strong>B</strong>
                        </button>
                        <button type="button" class="toolbar-btn" data-action="italic" title="斜体">
                            <em>I</em>
                        </button>
                        <button type="button" class="toolbar-btn" data-action="heading" title="标题">
                            H
                        </button>
                        <button type="button" class="toolbar-btn" data-action="link" title="链接">
                            🔗
                        </button>
                        <button type="button" class="toolbar-btn preview-btn" data-action="code" title="代码">
                            &lt;/&gt;
                        </button>
                        <button type="button" class="toolbar-btn" data-action="list" title="列表">
                            ≡
                        </button>
                        <div class="toolbar-divider"></div>
                        <button type="button" class="toolbar-btn" data-action="preview" title="预览">
                            👁
                        </button>
                    </div>

                    <!-- 编辑器容器 -->
                    <div class="editor-container">
                        <textarea name="content" id="markdownEditor" placeholder="支持Markdown格式，有什么想法想记录？" required
                                  class="w-full min-h-250 md:min-h-300 p-6 text-base border-none font-mono resize-y bg-white focus:outline-none"></textarea>
                        <div class="preview-container" id="previewContainer" style="display: none;">
                            <div class="preview-content" id="previewContent"></div>
                        </div>
                    </div>
                </div>
                <!-- 标签编辑区域和发布按钮 -->
                <div class="flex flex-col gap-4">
                    <div class="flex-1 min-w-0">
                        <label for="tagInput" class="block text-sm font-medium text-gray-700 mb-1.5">
                            添加标签 <span class="text-gray-400 text-xs">(输入标签名称后按 Enter 键添加，点击标签上的 × 可删除)</span>
                        </label>
                        <div class="tags-input-wrapper">
                            <div class="tags-container" id="tagsContainer"></div>
                            <input type="text" id="tagInput" placeholder="添加标签"
                                   class="flex-1 min-w-0 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200" />

                            <!-- 发布按钮 -->
                            <div class="flex-shrink-0 ml-2">
                                <button type="submit" form="postForm" class="px-4 py-1.5 bg-indigo-600 text-white font-semibold rounded-lg transition-all duration-300 hover:bg-indigo-700 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-indigo-100 text-sm shadow-sm">
                                    发布
                                </button>
                            </div>
                        </div>
                        <input type="hidden" name="tags" id="tagsField" value="[]" />
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 发布按钮由各页面自己定义，这里不需要重复定义 -->

<style>
/* 发布笔记模态框样式 - 使用Tailwind CSS */
.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* 动画效果 */
@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-fade-in {
    animation: modalFadeIn 0.3s ease;
}

.animate-slide-in {
    animation: modalSlideIn 0.3s ease;
}

/* Markdown工具栏样式 */
#postModal .markdown-toolbar {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
    border-radius: 12px 12px 0 0;
    flex-wrap: wrap;
}

#postModal .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    transition: all 0.2s ease;
}

#postModal .toolbar-btn:hover {
    background: #e2e8f0;
    color: #475569;
}

#postModal .toolbar-btn.active {
    background: #6366f1;
    color: white;
}

#postModal .toolbar-divider {
    width: 1px;
    height: 24px;
    background: #e2e8f0;
    margin: 0 4px;
}

#postModal .preview-btn.active {
    background: #10b981;
    color: white;
}

/* 编辑器容器样式 */
#postModal .editor-container {
    position: relative;
    border: 2px solid #e2e8f0;
    border-top: none;
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

#postModal .preview-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    overflow-y: auto;
}

#postModal .preview-content {
    padding: 1.5rem;
    line-height: 1.6;
    color: #374151;
}

/* 标签输入区域样式 */
#postModal .tags-input-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    transition: border-color 0.3s ease;
}

#postModal .tags-input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Markdown渲染样式 */
#postModal .preview-content h1, #postModal .preview-content h2, #postModal .preview-content h3,
#postModal .preview-content h4, #postModal .preview-content h5, #postModal .preview-content h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    color: #1f2937;
}

#postModal .preview-content h1 { font-size: 1.5em; }
#postModal .preview-content h2 { font-size: 1.3em; }
#postModal .preview-content h3 { font-size: 1.1em; }

#postModal .preview-content p {
    margin: 8px 0;
}

#postModal .preview-content strong {
    font-weight: 600;
    color: #1f2937;
}

#postModal .preview-content em {
    font-style: italic;
}

#postModal .preview-content code {
    background: #f1f5f9;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #e11d48;
}

#postModal .preview-content pre {
    background: #f8fafc;
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 12px 0;
    border: 1px solid #e2e8f0;
}

#postModal .preview-content pre code {
    background: none;
    padding: 0;
    color: #374151;
}

#postModal .preview-content ul, #postModal .preview-content ol {
    margin: 8px 0;
    padding-left: 24px;
}

#postModal .preview-content li {
    margin: 4px 0;
}

#postModal .preview-content a {
    color: #6366f1;
    text-decoration: underline;
}

#postModal .preview-content a:hover {
    color: #4f46e5;
}

#postModal .preview-content blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 16px;
    margin: 12px 0;
    color: #6b7280;
    font-style: italic;
}

/* 移动端编辑器内边距优化 */
@media (max-width: 768px) {
    #postModal textarea {
        padding: 1rem !important;
    }
}

@media (max-width: 480px) {
    #postModal textarea {
        padding: 0.75rem !important;
    }
}

</style>

<script>
// 注意：TagManager类现在使用外部的tags.js文件中的实现

// Markdown编辑器控制器
class MarkdownEditor {
    constructor(textareaId, previewContainerId, previewContentId) {
        this.textarea = document.getElementById(textareaId);
        this.previewContainer = document.getElementById(previewContainerId);
        this.previewContent = document.getElementById(previewContentId);
        this.isPreviewMode = false;

        this.init();
    }

    init() {
        // 绑定工具栏按钮事件 - 只选择发布模态框内的按钮
        document.querySelectorAll('#postModal .toolbar-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleToolbarClick(e));
        });
    }

    handleToolbarClick(e) {
        e.preventDefault();
        const action = e.target.closest('.toolbar-btn').getAttribute('data-action');

        switch(action) {
            case 'bold':
                this.insertMarkdown('**', '**', '粗体文本');
                break;
            case 'italic':
                this.insertMarkdown('*', '*', '斜体文本');
                break;
            case 'heading':
                this.insertMarkdown('## ', '', '标题');
                break;
            case 'link':
                this.insertMarkdown('[', '](http://)', '链接文本');
                break;
            case 'code':
                this.insertMarkdown('`', '`', '代码');
                break;
            case 'list':
                this.insertMarkdown('- ', '', '列表项');
                break;
            case 'preview':
                this.togglePreview(e.target.closest('.toolbar-btn'));
                break;
        }
    }

    insertMarkdown(before, after, placeholder) {
        const start = this.textarea.selectionStart;
        const end = this.textarea.selectionEnd;
        const selectedText = this.textarea.value.substring(start, end);
        const text = selectedText || placeholder;

        const newText = before + text + after;
        this.textarea.value = this.textarea.value.substring(0, start) + newText + this.textarea.value.substring(end);

        // 设置光标位置
        const newStart = start + before.length;
        const newEnd = newStart + text.length;
        this.textarea.focus();
        this.textarea.setSelectionRange(newStart, newEnd);
    }

    async togglePreview(button) {
        this.isPreviewMode = !this.isPreviewMode;

        if (this.isPreviewMode) {
            // 显示预览
            button.classList.add('active');
            await this.renderPreview();
            this.previewContainer.style.display = 'block';
        } else {
            // 隐藏预览
            button.classList.remove('active');
            this.previewContainer.style.display = 'none';
        }
    }

    async renderPreview() {
        const content = this.textarea.value;
        try {
            // 发送到后端渲染Markdown
            const response = await fetch('/api/markdown/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({ content: content })
            });

            if (response.ok) {
                const data = await response.json();
                this.previewContent.innerHTML = data.rendered_content;
            } else {
                // 如果API不可用，使用简单的客户端渲染
                this.previewContent.innerHTML = this.simpleMarkdownRender(content);
            }
        } catch (error) {
            // 使用简单的客户端渲染作为后备
            this.previewContent.innerHTML = this.simpleMarkdownRender(content);
        }
    }

    simpleMarkdownRender(content) {
        // 简单的客户端Markdown渲染（后备方案）
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            .replace(/^\- (.*$)/gim, '<li>$1</li>')
            .replace(/\n/g, '<br>');
    }

    getValue() {
        return this.textarea.value;
    }

    setValue(value) {
        this.textarea.value = value;
    }

    clear() {
        this.textarea.value = '';
        this.previewContent.innerHTML = '';
    }
}

// 发布笔记模态框控制器
class PostModalController {
    constructor(options = {}) {
        this.createPostUrl = options.createPostUrl || '/post/create';
        this.onPostCreated = options.onPostCreated || this.defaultOnPostCreated.bind(this);

        this.init();
    }
    
    init() {
        this.postButton = document.getElementById('postButton');
        this.postModal = document.getElementById('postModal');
        this.postForm = document.getElementById('postForm');

        // 初始化Markdown编辑器
        this.markdownEditor = new MarkdownEditor('markdownEditor', 'previewContainer', 'previewContent');

        // 初始化标签管理器
        this.tagManager = new TagManager({
            tagInput: document.getElementById('tagInput'),
            tagsContainer: document.getElementById('tagsContainer'),
            tagsField: document.getElementById('tagsField')
        });

        this.bindEvents();
    }
    
    bindEvents() {
        // 发布按钮点击事件
        this.postButton.addEventListener('click', () => this.showModal());
        
        // 关闭模态框事件
        this.postModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
            button.addEventListener('click', () => this.hideModal());
        });
        
        // 点击背景关闭（仅通过关闭按钮）
        this.postModal.addEventListener('click', (event) => {
            if (event.target.classList.contains('close-button')) {
                this.hideModal();
            }
        });
        
        // 表单提交事件
        this.postForm.addEventListener('submit', (e) => this.handleSubmit(e));
    }
    
    showModal() {
        document.body.style.overflow = 'hidden'; // 防止背景滚动
        this.postModal.classList.add('show');
    }

    hideModal() {
        document.body.style.overflow = ''; // 恢复滚动
        this.postModal.classList.remove('show');
        this.postForm.reset();
        this.tagManager.clearTags();
    }
    
    handleSubmit(e) {
        e.preventDefault();
        
        // 检查未添加的标签
        this.tagManager.processRemainingTag();
        
        const formData = new FormData(this.postForm);
        formData.append('tags', JSON.stringify(this.tagManager.getTags()));
        
        fetch(this.createPostUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(formData)
        })
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => {
                    try {
                        const errorData = JSON.parse(text);
                        throw new Error(errorData.message || '请求失败');
                    } catch {
                        throw new Error(`服务器错误 (${response.status}): ${text.slice(0, 100)}`);
                    }
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                this.onPostCreated(data.post);
            } else {
                alert('发布失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('发布失败，请重试');
        })
        .finally(() => {
            this.hideModal();
        });
    }
    
    defaultOnPostCreated(post) {
        // 尝试动态添加新笔记到页面，如果失败则刷新页面
        try {
            // 创建新的微博卡片
            const postCard = document.createElement('div');
            postCard.className = 'card bg-white rounded-2xl p-8 shadow-sm border border-gray-200 relative overflow-hidden md:p-6';
            postCard.setAttribute('data-post-id', post.id);

            // 构建微博卡片内容
            let tagsHtml = '';
            if (post.tags && post.tags.length > 0) {
                post.tags.forEach(tag => {
                    const isActive = post.current_tag === tag ? 'active bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-transparent' : '';
                    tagsHtml += `<a href="/?tag=${encodeURIComponent(tag)}" class="tag inline-flex items-center px-3 py-1 text-xs font-semibold bg-gradient-to-br from-indigo-50 to-purple-50 text-indigo-600 no-underline rounded-full border border-indigo-200 transition-all duration-300 hover:bg-gradient-to-br hover:from-indigo-100 hover:to-purple-100 hover:-translate-y-0.5 hover:shadow-sm ${isActive}">${tag}</a>`;
                });
            }

            postCard.innerHTML = `
                <div class="card-header mb-0">
                    <div class="header-row flex items-center justify-between mb-4">
                        <div class="post-time text-xs text-gray-500 font-medium">${post.created_at}</div>
                        <div class="btn-group flex gap-2">
                            <button class="btn-secondary px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-indigo-600 text-white hover:bg-indigo-700 hover:-translate-y-0.5 edit-post md:px-2 md:py-1 md:text-xs md:min-w-[40px]" data-post-id="${post.id}">编辑</button>
                            <button class="btn-danger px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-500 text-white hover:bg-red-600 hover:-translate-y-0.5 delete-post md:px-2 md:py-1 md:text-xs md:min-w-[40px]" data-post-id="${post.id}">删除</button>
                        </div>
                    </div>
                    <div class="post-content text-base leading-relaxed text-gray-800 mb-6 break-words markdown-content" id="content-${post.id}">${post.rendered_content}</div>
                    <div class="tags-container flex flex-wrap gap-2">
                        ${tagsHtml}
                    </div>
                </div>
            `;

            // 将新微博添加到列表顶部
            const postsContainer = document.querySelector('.posts-list');
            if (postsContainer) {
                const firstCard = postsContainer.querySelector('.card');
                if (firstCard) {
                    postsContainer.insertBefore(postCard, firstCard);
                } else {
                    postsContainer.appendChild(postCard);
                }
            } else {
                // 如果没有posts-list容器，则添加到container中分页导航之前
                const container = document.querySelector('.container');
                const pagination = container.querySelector('.pagination');
                if (pagination) {
                    container.insertBefore(postCard, pagination);
                } else {
                    container.appendChild(postCard);
                }
            }
        } catch (error) {
            console.error('动态添加笔记失败:', error);
            // 如果动态添加失败，则刷新页面
            window.location.reload();
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否存在必要的元素
    if (document.getElementById('postButton') && document.getElementById('postModal')) {
        // 创建发布笔记控制器
        window.postModalController = new PostModalController({
            createPostUrl: window.API_URLS?.createPost || '/post/create',
            onPostCreated: window.onPostCreated || undefined
        });
    }
});
</script>
