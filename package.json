{"name": "haonote-web-tests", "version": "1.0.0", "description": "Playwright tests for HaoNote Web application", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "install-browsers": "playwright install"}, "devDependencies": {"@playwright/test": "^1.40.0"}, "keywords": ["playwright", "testing", "modal", "responsive"], "author": "HaoNote Team", "license": "MIT"}